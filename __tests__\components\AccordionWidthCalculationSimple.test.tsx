import React from 'react'
import { render, act } from '@testing-library/react'
import '@testing-library/jest-dom'
import Accordion from '@/app/components/Accordion'

// Mock the hooks and dependencies
jest.mock('@/app/hooks/useIsXlScreen', () => ({
  useIsXlScreen: jest.fn(),
}))

jest.mock('usehooks-ts', () => ({
  useResizeObserver: jest.fn(),
}))

jest.mock('lodash', () => ({
  min: jest.fn((arr: number[]) => Math.min(...arr.filter(Boolean))),
}))

jest.mock('@/lib/getInnerWidth', () => ({
  getInnerWidth: jest.fn(),
}))

// Import the mocked functions after mocking
import { useIsXlScreen } from '@/app/hooks/useIsXlScreen'
import { useResizeObserver } from 'usehooks-ts'
import { getInnerWidth } from '@/lib/getInnerWidth'

const mockUseIsXlScreen = useIsXlScreen as jest.MockedFunction<typeof useIsXlScreen>
const mockUseResizeObserver = useResizeObserver as jest.MockedFunction<typeof useResizeObserver>
const mockGetInnerWidth = getInnerWidth as jest.MockedFunction<typeof getInnerWidth>

// Test data
const mockMultipleItems = [
  {
    id: 'item-1',
    title: 'Test Item 1',
    text: 'This is the content for test item 1.',
  },
  {
    id: 'item-2',
    title: 'Test Item 2',
    text: 'This is the content for test item 2.',
  },
  {
    id: 'item-3',
    title: 'Test Item 3',
    text: 'This is the content for test item 3.',
  },
]

describe('Accordion Width Calculation - Core Functionality', () => {
  let mockResizeCallback: () => void

  beforeEach(() => {
    jest.clearAllMocks()

    // Setup default mock implementations
    mockUseResizeObserver.mockImplementation(options => {
      mockResizeCallback = options.onResize || (() => {})
      return { width: 0, height: 0 }
    })

    // Mock getBoundingClientRect for accordion items to return different widths
    // This simulates the real behavior where accordion items have different widths
    let callCount = 0
    Element.prototype.getBoundingClientRect = jest.fn(() => {
      callCount++
      // Return different widths for different elements to test min calculation
      const width = callCount <= 3 ? 120 : 800 // First 3 calls are accordion items, rest are container
      return {
        width,
        height: 40,
        top: 0,
        left: 0,
        bottom: 40,
        right: width,
        x: 0,
        y: 0,
        toJSON: jest.fn(),
      }
    })
  })

  describe('Screen size behavior and width calculation', () => {
    it('should render horizontal layout on xl screen with width calculation logic', () => {
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(1400)

      const { container } = render(<Accordion items={mockMultipleItems} />)

      // Should render in horizontal orientation
      expect(container.querySelector('[data-orientation="horizontal"]')).toBeTruthy()

      // Verify that the ScrollArea component exists and has the expected structure
      const scrollArea = container.querySelector('.h-full')
      expect(scrollArea).toBeTruthy()

      // The width calculation should be triggered on xl screens
      // We can't easily test the exact pixel value due to test environment limitations,
      // but we can verify the component structure and behavior through snapshots
      expect(container).toMatchSnapshot('xl-screen-horizontal-layout-1400px')
    })

    it('should render vertical layout on mobile/tablet screens', () => {
      mockUseIsXlScreen.mockReturnValue(false)
      mockGetInnerWidth.mockReturnValue(800)

      const { container } = render(<Accordion items={mockMultipleItems} />)

      // Should render in vertical orientation
      expect(container.querySelector('[data-orientation="vertical"]')).toBeTruthy()

      // Verify ScrollArea exists
      const scrollArea = container.querySelector('.h-full')
      expect(scrollArea).toBeTruthy()

      expect(container).toMatchSnapshot('mobile-vertical-layout-800px')
    })
  })

  describe('Different screen width scenarios', () => {
    it('should handle standard desktop width (1400px) with width calculation', () => {
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(1400)

      const { container } = render(<Accordion items={mockMultipleItems} />)

      // Should trigger width calculation logic for 1400px screen
      expect(container.querySelector('[data-orientation="horizontal"]')).toBeTruthy()
      expect(container.querySelector('.h-full')).toBeTruthy()

      expect(container).toMatchSnapshot('desktop-1400px-width-calculation')
    })

    it('should handle smaller xl screen width (1300px) with width calculation', () => {
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(1300)

      const { container } = render(<Accordion items={mockMultipleItems} />)

      // Should trigger width calculation logic for 1300px screen
      expect(container.querySelector('[data-orientation="horizontal"]')).toBeTruthy()
      expect(container.querySelector('.h-full')).toBeTruthy()

      expect(container).toMatchSnapshot('desktop-1300px-width-calculation')
    })

    it('should handle very wide screen (1920px) with width calculation', () => {
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(1920)

      const { container } = render(<Accordion items={mockMultipleItems} />)

      // Should trigger width calculation logic for 1920px screen
      expect(container.querySelector('[data-orientation="horizontal"]')).toBeTruthy()
      expect(container.querySelector('.h-full')).toBeTruthy()

      expect(container).toMatchSnapshot('desktop-1920px-width-calculation')
    })
  })

  describe('Resize behavior and width recalculation', () => {
    it('should handle screen resize from small to large xl screen', () => {
      // Start with smaller xl screen
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(1300)

      const { container } = render(<Accordion items={mockMultipleItems} />)

      // Initial state should be horizontal
      expect(container.querySelector('[data-orientation="horizontal"]')).toBeTruthy()

      // Simulate screen resize to larger width
      mockGetInnerWidth.mockReturnValue(1600)

      act(() => {
        mockResizeCallback()
      })

      // Should still be horizontal and trigger width recalculation
      expect(container.querySelector('[data-orientation="horizontal"]')).toBeTruthy()
      expect(container).toMatchSnapshot('after-resize-from-1300px-to-1600px')
    })

    it('should handle screen resize from large to small xl screen', () => {
      // Start with larger xl screen
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(1600)

      const { container } = render(<Accordion items={mockMultipleItems} />)

      // Initial state should be horizontal
      expect(container.querySelector('[data-orientation="horizontal"]')).toBeTruthy()

      // Simulate screen resize to smaller width
      mockGetInnerWidth.mockReturnValue(1300)

      act(() => {
        mockResizeCallback()
      })

      // Should still be horizontal and trigger width recalculation
      expect(container.querySelector('[data-orientation="horizontal"]')).toBeTruthy()
      expect(container).toMatchSnapshot('after-resize-from-1600px-to-1300px')
    })
  })

  describe('Screen size transitions and layout changes', () => {
    it('should transition from mobile to xl screen layout', () => {
      // Start on mobile
      mockUseIsXlScreen.mockReturnValue(false)
      mockGetInnerWidth.mockReturnValue(800)

      const { container, rerender } = render(<Accordion items={mockMultipleItems} />)

      // Should be vertical on mobile
      expect(container.querySelector('[data-orientation="vertical"]')).toBeTruthy()
      expect(container.querySelector('.h-full')).toBeTruthy()

      // Transition to xl screen
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(1400)

      rerender(<Accordion items={mockMultipleItems} />)

      // Should now be horizontal and trigger width calculation
      expect(container.querySelector('[data-orientation="horizontal"]')).toBeTruthy()
      expect(container.querySelector('.h-full')).toBeTruthy()

      expect(container).toMatchSnapshot('transition-mobile-to-xl-1400px')
    })

    it('should transition from xl screen to mobile layout', () => {
      // Start on xl screen
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(1400)

      const { container, rerender } = render(<Accordion items={mockMultipleItems} />)

      // Should be horizontal on xl
      expect(container.querySelector('[data-orientation="horizontal"]')).toBeTruthy()
      expect(container.querySelector('.h-full')).toBeTruthy()

      // Transition to mobile
      mockUseIsXlScreen.mockReturnValue(false)
      mockGetInnerWidth.mockReturnValue(800)

      rerender(<Accordion items={mockMultipleItems} />)

      // Should now be vertical
      expect(container.querySelector('[data-orientation="vertical"]')).toBeTruthy()
      expect(container.querySelector('.h-full')).toBeTruthy()

      expect(container).toMatchSnapshot('transition-xl-to-mobile-800px')
    })
  })

  describe('Different item configurations and width calculations', () => {
    it('should handle single item with width calculation', () => {
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(1400)

      const singleItem = [mockMultipleItems[0]]
      const { container } = render(<Accordion items={singleItem} />)

      // Should trigger width calculation for single item: 1400 - (120 * 1) = 1280px
      expect(container.querySelector('[data-orientation="horizontal"]')).toBeTruthy()
      expect(container.querySelector('.h-full')).toBeTruthy()

      expect(container).toMatchSnapshot('single-item-xl-screen-1400px')
    })

    it('should handle many items with width calculation', () => {
      mockUseIsXlScreen.mockReturnValue(true)
      mockGetInnerWidth.mockReturnValue(1600)

      const manyItems = [
        ...mockMultipleItems,
        { id: 'item-4', title: 'Item 4', text: 'Content 4' },
        { id: 'item-5', title: 'Item 5', text: 'Content 5' },
      ]

      const { container } = render(<Accordion items={manyItems} />)

      // Should trigger width calculation for many items: 1600 - (120 * 5) = 1000px
      expect(container.querySelector('[data-orientation="horizontal"]')).toBeTruthy()
      expect(container.querySelector('.h-full')).toBeTruthy()

      expect(container).toMatchSnapshot('many-items-xl-screen-1600px')
    })
  })
})
