// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Accordion Width Calculation - Core Functionality Different item configurations and width calculations should handle many items with width calculation: many-items-xl-screen-1600px 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:r1p:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:r1o:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r1o:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:r1p:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  This is the content for test item 1.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r1r:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r1q:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r1q:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r1r:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r1t:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r1s:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r1s:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r1t:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r1v:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r1u:"
          type="button"
        >
          Item 4
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r1u:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r1v:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 800px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r21:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r20:"
          type="button"
        >
          Item 5
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r20:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r21:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 800px;"
      />
    </div>
  </div>
</div>
`;

exports[`Accordion Width Calculation - Core Functionality Different item configurations and width calculations should handle single item with width calculation: single-item-xl-screen-1400px 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:r1n:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:r1m:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r1m:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:r1n:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  This is the content for test item 1.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Accordion Width Calculation - Core Functionality Different screen width scenarios should handle smaller xl screen width (1300px) with width calculation: desktop-1300px-width-calculation 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:rj:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:ri:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:ri:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:rj:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  This is the content for test item 1.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:rl:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:rk:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:rk:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:rl:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:rn:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:rm:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:rm:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:rn:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      />
    </div>
  </div>
</div>
`;

exports[`Accordion Width Calculation - Core Functionality Different screen width scenarios should handle standard desktop width (1400px) with width calculation: desktop-1400px-width-calculation 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:rd:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:rc:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:rc:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:rd:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  This is the content for test item 1.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:rf:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:re:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:re:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:rf:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:rh:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:rg:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:rg:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:rh:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      />
    </div>
  </div>
</div>
`;

exports[`Accordion Width Calculation - Core Functionality Different screen width scenarios should handle very wide screen (1920px) with width calculation: desktop-1920px-width-calculation 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:rp:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:ro:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:ro:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:rp:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  This is the content for test item 1.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:rr:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:rq:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:rq:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:rr:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:rt:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:rs:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:rs:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:rt:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      />
    </div>
  </div>
</div>
`;

exports[`Accordion Width Calculation - Core Functionality Resize behavior and width recalculation should handle screen resize from large to small xl screen: after-resize-from-1600px-to-1300px 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:r15:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:r14:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r14:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:r15:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  This is the content for test item 1.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r17:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r16:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r16:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r17:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r19:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r18:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r18:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r19:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      />
    </div>
  </div>
</div>
`;

exports[`Accordion Width Calculation - Core Functionality Resize behavior and width recalculation should handle screen resize from small to large xl screen: after-resize-from-1300px-to-1600px 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:rv:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:ru:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:ru:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:rv:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  This is the content for test item 1.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r11:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r10:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r10:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r11:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r13:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r12:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r12:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r13:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      />
    </div>
  </div>
</div>
`;

exports[`Accordion Width Calculation - Core Functionality Screen size behavior and width calculation should render horizontal layout on xl screen with width calculation logic: xl-screen-horizontal-layout-1400px 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:r1:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:r0:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r0:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:r1:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  This is the content for test item 1.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r3:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r2:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r2:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r3:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r5:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r4:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r4:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r5:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      />
    </div>
  </div>
</div>
`;

exports[`Accordion Width Calculation - Core Functionality Screen size behavior and width calculation should render vertical layout on mobile/tablet screens: mobile-vertical-layout-800px 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="vertical"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="vertical"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="vertical"
        data-state="open"
      >
        <button
          aria-controls="radix-:r7:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="vertical"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:r6:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r6:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="vertical"
        data-state="open"
        id="radix-:r7:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  This is the content for test item 1.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="vertical"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="vertical"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r9:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="vertical"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r8:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r8:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="vertical"
        data-state="closed"
        hidden=""
        id="radix-:r9:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="vertical"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="vertical"
        data-state="closed"
      >
        <button
          aria-controls="radix-:rb:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="vertical"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:ra:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:ra:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="vertical"
        data-state="closed"
        hidden=""
        id="radix-:rb:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      />
    </div>
  </div>
</div>
`;

exports[`Accordion Width Calculation - Core Functionality Screen size transitions and layout changes should transition from mobile to xl screen layout: transition-mobile-to-xl-1400px 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:r1b:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:r1a:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r1a:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:r1b:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  This is the content for test item 1.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r1d:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r1c:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r1c:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r1d:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r1f:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r1e:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r1e:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:r1f:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      />
    </div>
  </div>
</div>
`;

exports[`Accordion Width Calculation - Core Functionality Screen size transitions and layout changes should transition from xl screen to mobile layout: transition-xl-to-mobile-800px 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="vertical"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="vertical"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="vertical"
        data-state="open"
      >
        <button
          aria-controls="radix-:r1h:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="vertical"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:r1g:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r1g:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="vertical"
        data-state="open"
        id="radix-:r1h:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  This is the content for test item 1.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="vertical"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="vertical"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r1j:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="vertical"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r1i:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r1i:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="vertical"
        data-state="closed"
        hidden=""
        id="radix-:r1j:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="vertical"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="vertical"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r1l:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="vertical"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r1k:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r1k:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="vertical"
        data-state="closed"
        hidden=""
        id="radix-:r1l:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 40px; --radix-collapsible-content-width: 120px;"
      />
    </div>
  </div>
</div>
`;
